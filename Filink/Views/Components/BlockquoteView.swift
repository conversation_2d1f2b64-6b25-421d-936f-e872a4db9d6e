//
//  BlockquoteView.swift
//  Filink
//
//  SwiftUI版本的引用块组件
//

import SwiftUI

/// SwiftUI版本的引用块视图
struct BlockquoteView: View {
    let contentItems: [ContentItem]
    
    var body: some View {
        HStack(alignment: .top, spacing: 0) {
            // 左侧蓝色线条
            Rectangle()
                .fill(Color.blue)
                .frame(width: 4)

            // 内容区域 - 占满剩余宽度
            VStack(alignment: .leading, spacing: 4) {
                ForEach(contentItems, id: \.id) { item in
                    renderContentItem(item)
                }
            }
            .frame(maxWidth: .infinity, alignment: .leading)
            .padding(.horizontal, 12)
            .padding(.vertical, 8)
        }
        .background(Color(.systemGray6)) // 背景色应用到整个HStack
        .frame(maxWidth: .infinity) // 确保整个容器占满父视图宽度
        .padding(.vertical, 4)
    }
    
    @ViewBuilder
    private func renderContentItem(_ item: ContentItem) -> some View {
        switch item {
        case .text(let text, let isBold, let isItalic, let isUnderline):
            Text(createAttributedString(text: text, isBold: isBold, isItalic: isItalic, isUnderline: isUnderline))
                .fixedSize(horizontal: false, vertical: true)
                
        case .link(let text, let url):
            Link(text, destination: url)
                .font(.system(size: 16))
                .foregroundColor(.blue)
                
        case .newline:
            Text("")
                .frame(height: 4)
                
        case .heading1(let text):
            Text(text)
                .font(.title.weight(.bold))
                .foregroundColor(.primary)
                .padding(.vertical, 2)

        case .heading2(let text):
            Text(text)
                .font(.title2.weight(.bold))
                .foregroundColor(.primary)
                .padding(.vertical, 2)

        case .heading3(let text):
            Text(text)
                .font(.title3.weight(.bold))
                .foregroundColor(.primary)
                .padding(.vertical, 2)
                
        case .listItem(let text, let level, let isOrdered, let number):
            HStack(alignment: .top, spacing: 4) {
                Text(isOrdered ? "\(number ?? 1)." : "•")
                    .font(.system(size: 16))
                    .foregroundColor(.secondary)
                    .padding(.leading, CGFloat(level * 16))
                
                Text(text)
                    .font(.system(size: 16))
                    .foregroundColor(.primary)
                    .fixedSize(horizontal: false, vertical: true)
            }
            
        case .spoiler(let text, let isRevealed):
            Text(text)
                .font(.system(size: 16))
                .foregroundColor(isRevealed ? .primary : .clear)
                .background(isRevealed ? Color.clear : Color.black)
                .cornerRadius(4)
                
        case .horizontalRule:
            Divider()
                .padding(.vertical, 8)
                
        default:
            // 对于其他复杂类型，显示占位文本
            Text("不支持的内容类型")
                .font(.system(size: 14))
                .foregroundColor(.secondary)
                .italic()
        }
    }

    // 创建带格式的AttributedString (iOS 15兼容)
    private func createAttributedString(text: String, isBold: Bool, isItalic: Bool, isUnderline: Bool) -> AttributedString {
        var attributedString = AttributedString(text)

        // 设置字体
        var font = UIFont.systemFont(ofSize: 16)
        if isBold && isItalic {
            font = UIFont.systemFont(ofSize: 16, weight: .bold).withTraits(.traitItalic)
        } else if isBold {
            font = UIFont.boldSystemFont(ofSize: 16)
        } else if isItalic {
            font = UIFont.italicSystemFont(ofSize: 16)
        }

        attributedString.font = font

        // 设置下划线
        if isUnderline {
            attributedString.underlineStyle = .single
        }

        return attributedString
    }
}
