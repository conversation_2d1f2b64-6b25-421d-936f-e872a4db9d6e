import SwiftUI

// FeedHeaderView 整合了原有的 HeaderView 和 FeedControlView
struct FeedHeaderView: View {
    // 来自原 HeaderView 的属性
    var newOffset: CGFloat
    var oldOffset: CGFloat
    var headerHeight: CGFloat
    var topSafeArea: CGFloat
    @Binding var feedMode: FeedMode
    @Binding var selectedCategoryId: Int
    @Binding var showSearch: Bool
    var searchIconState: SearchIconState
    var onModeChange: (FeedMode) -> Void
    var onTitleTap: (() -> Void)? = nil
    var onTitleDoubleTap: (() -> Void)? = nil
    var onCategorySelected: ((CategoryItem) -> Void)? = nil

    @State private var categories = Filink.categories
    @State private var lastTitleTapTime: Date? = nil
    @State private var titleTapScale: CGFloat = 1.0
    @State private var titleTapOpacity: Double = 1.0
    @State private var titleTapColor: Color = .primary

    // 来自原 FeedControlView 的属性
    @State private var showControlPanel = false
    @EnvironmentObject private var settingsManager: SettingsManager
    @State private var tempShowPinnedPosts: Bool = true
    @State private var tempShowTagsFooter: Bool = true

    private let extraShrinkAmount: CGFloat = 30.0
    private let titleScaleFactor: CGFloat = 0.2
    private let titleOffsetFactor: CGFloat = -5.0
    private let menuIconOpacityFactor: CGFloat = 0.7

    private var headerState: (yOffset: CGFloat, showCategories: Bool) {
        let currentYOffset = newOffset
        let _ = oldOffset

        let criticalThreshold = -2 * (headerHeight - 90 - topSafeArea)
        let maxShrinkStage1 = headerHeight - 90 - topSafeArea

        if maxShrinkStage1 < 0 {
            let yOffset = currentYOffset > 0 ? 0 : min(0, currentYOffset)
            return (yOffset: yOffset, showCategories: true)
        }
        if currentYOffset > 0 {
            return (yOffset: 0, showCategories: true)
        }

        if currentYOffset < criticalThreshold {
            if newOffset < oldOffset {
                let yOffset = -(maxShrinkStage1 + extraShrinkAmount)
                return (yOffset: yOffset, showCategories: false)
            } else {
                let yOffset = -maxShrinkStage1
                return (yOffset: yOffset, showCategories: true)
            }
        } else {
            let scrollUp = -currentYOffset
            let desiredShrink = scrollUp * 0.5
            let yOffset = -min(desiredShrink, maxShrinkStage1)
            return (yOffset: yOffset, showCategories: true)
        }
    }

    func getProgress() -> CGFloat {
        let scrollUpDistance = -self.newOffset
        let denominator = (self.headerHeight - 100 - self.topSafeArea)
        guard denominator > 0 else { return 0 }
        let progress = scrollUpDistance / denominator
        return min(max(progress, 0), 1)
    }

    var body: some View {
        let currentHeaderState = headerState
        let titleProgress = getProgress()

        VStack(spacing: 0) {
            ZStack {
                VStack(spacing: 0) {
                    VStack {
                        Spacer(minLength: topSafeArea)

                        VStack(spacing: 4) {
                            Text("Where possible begins")
                                .font(.system(size: 24, weight: .semibold, design: .rounded))
                                .foregroundStyle(
                                    LinearGradient(
                                        colors: [.blue, .purple, .pink],
                                        startPoint: .leading,
                                        endPoint: .trailing
                                    )
                                )
                                .opacity(1.0 - titleProgress * 1)
                                .scaleEffect(1.0 - titleProgress * 0.3, anchor: .center)
                                .animation(.easeInOut(duration: 0.3), value: titleProgress)
                        }
                        .padding(.bottom, 8)

                        HStack(alignment: .center) {
                            Text("Linux.do")
                                .font(.title2.bold())
                                .foregroundColor(titleTapColor)
                                .scaleEffect(
                                    1.0 + (titleProgress * titleScaleFactor),
                                    anchor: .leading
                                )
                                .scaleEffect(titleTapScale)
                                .opacity(titleTapOpacity)
                                .onTapGesture {
                                    triggerTapAnimation()
                                    let now = Date()
                                    if let lastTap = lastTitleTapTime,
                                        now.timeIntervalSince(lastTap) < 0.3
                                    {
                                        lastTitleTapTime = nil
                                        onTitleDoubleTap?()
                                    } else {
                                        lastTitleTapTime = now
                                        DispatchQueue.main.asyncAfter(
                                            deadline: .now() + 0.3
                                        ) {
                                            if self.lastTitleTapTime == now {
                                                onTitleTap?()
                                                self.lastTitleTapTime = nil
                                            }
                                        }
                                    }
                                }
            Menu {
                                ForEach(FeedMode.allCases, id: \.self)
                                { mode in
                                    Button(action: {
                                        feedMode = mode
                                        onModeChange(mode)
                                    }) {
                                        Label(
                                            mode.rawValue,
                                            systemImage: mode.icon)
                                    }
                                }
                                
                                Divider()
                                
                                // 性能优化选项
                                Toggle(isOn: .init(
                                    get: { UserDefaults.standard.bool(forKey: "useOptimizedFeedList") },
                                    set: { UserDefaults.standard.set($0, forKey: "useOptimizedFeedList") }
                                )) {
                                    Label("优化滚动性能", systemImage: "bolt")
                                }
                            }
                                VStack(spacing: 2) {
                                    Image(systemName: "chevron.up")
                                        .font(.system(size: 14))
                                    Image(systemName: "chevron.down")
                                        .font(.system(size: 14))
                                }
                            }
                            .padding(.leading, (titleProgress * 30))
                            .frame(width: 20, height: 20)
                            .opacity(
                                1.0 - titleProgress * menuIconOpacityFactor)

                            Spacer()

                            // 整合 FeedControlView
                            feedControlMenu()
                                .padding(.trailing, 8)

                            Button(action: {
                                if searchIconState == .search {
                                    showSearch = true
                                }
                            }) {
                                Group {
                                    switch searchIconState {
                                    case .search:
                                        Image(systemName: "magnifyingglass")
                                            .font(.title3)
                                            .foregroundColor(.primary)
                                    case .loading:
                                        ProgressView()
                                            .progressViewStyle(
                                                CircularProgressViewStyle()
                                            )
                                            .frame(width: 20, height: 20)
                                    case .success:
                                        Image(
                                            systemName: "checkmark.circle.fill"
                                        )
                                        .font(.title3)
                                        .foregroundColor(.green)
                                    }
                                }
                                .frame(width: 24, height: 24)
                            }
                        }
                        .padding(
                            .bottom, currentHeaderState.showCategories ? 4 : 14
                        )
                        .padding(.horizontal, 16)
                    }
                    .frame(height: headerHeight - 50)

                    if currentHeaderState.showCategories {
                        VStack(spacing: 0) {
                            ScrollView(.horizontal, showsIndicators: false) {
                                HStack {
                                    ForEach(categories) { category in
                                        Button(action: {
                                            selectedCategoryId = category.id
                                            print(
                                                "【调试】点击了 \(category.name) (ID: \(category.id))"
                                            )
                                            onCategorySelected?(category)
                                        }) {
                                            HStack {
                                                Image(systemName: category.icon)
                                                Text(category.name)
                                            }
                                            .font(.subheadline)
                                            .padding(.horizontal, 12)
                                            .padding(.vertical, 7)
                                            .background(
                                                selectedCategoryId
                                                    == category.id
                                                    ? Color.blue
                                                    : Color.blue.opacity(0.2)
                                            )
                                            .cornerRadius(15)
                                            .foregroundColor(
                                                selectedCategoryId
                                                    == category.id
                                                    ? .white : .blue)
                                        }
                                    }
                                }
                                .padding(.horizontal)
                            }
                        }
                        .frame(height: 50)
                        .transition(
                            AnyTransition.opacity
                                .combined(with: .move(edge: .top))
                                .combined(
                                    with: .scale(scale: 0.95, anchor: .top))
                        )
                    }
                }
            }
            .background(.ultraThinMaterial)
            .frame(height: headerHeight)
            .offset(y: currentHeaderState.yOffset)
        }
        .ignoresSafeArea(edges: .top)
        .animation(
            .interactiveSpring(
                response: 0.35, dampingFraction: 0.82, blendDuration: 0.25),
            value: currentHeaderState.yOffset
        )
        .animation(
            .easeInOut(duration: 0.25), value: currentHeaderState.showCategories
        )
    }

    // 提取 FeedControlView 的逻辑为一个子视图构建器
    @ViewBuilder
    private func feedControlMenu() -> some View {
        Button(action: {
            showControlPanel.toggle()
        }) {
            Image(systemName: "slider.horizontal.3")
                .font(.system(size: 18))
                .foregroundColor(.primary)
        }
        .sheet(isPresented: $showControlPanel) {
            NavigationView {
                List {
                    Section(header: Text("帖子显示设置")) {
                        Toggle(
                            "显示置顶帖子",
                            isOn: $tempShowPinnedPosts
                        )

                        Toggle(
                            "显示标签栏",
                            isOn: $tempShowTagsFooter
                        )
                    }


                }
                .navigationTitle("Feed 设置")
                .navigationBarTitleDisplayMode(.inline)
                .toolbar {
                    ToolbarItem(placement: .navigationBarTrailing) {
                        Button("完成") {
                            settingsManager.showPinnedPosts = tempShowPinnedPosts
                            settingsManager.showTagsFooter = tempShowTagsFooter
                            showControlPanel = false
                        }
                    }
                }
            }
            .onAppear {
                tempShowPinnedPosts = settingsManager.showPinnedPosts
                tempShowTagsFooter = settingsManager.showTagsFooter
            }
            .interactiveDismissDisabled(true)
        }
    }

    private func triggerTapAnimation() {
        withAnimation(.easeIn(duration: 0.1)) {
            titleTapColor = .blue
            titleTapScale = 0.95
            titleTapOpacity = 0.8
        }

        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            withAnimation(.spring(response: 0.3, dampingFraction: 0.7, blendDuration: 0)) {
                titleTapColor = .primary
                titleTapScale = 1.0
                titleTapOpacity = 1.0
            }
        }
    }
}
